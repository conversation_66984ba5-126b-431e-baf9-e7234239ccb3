import React from 'react';
import { BuiltinUiState, Service, useObservableState, WidgetService } from '@tencent/workbench';
import { CoCraftBridgeAPI } from '@tencent/cocraft-api';
import { EditorService } from '../editorServices';
import { assignObject, calcBlankPosition, rounding } from './utils';
import { DEFAULT_LOADING_MASK_OPTIONS, DEFAULT_LOADING_MASK_SIZE, OUTER_GAP } from './constant';
import { useCurrentZoom } from '~workspace/hooks/currentZoomHook';
import { useCurrentBounds } from '~workspace/hooks/currentBoundsHook';
import { defaultMarginLeft } from '~workspace/constants/ui';

export type H2dLoadingMaskConfig = {
  type?: 'app' | 'pc';
  width?: number;
  height?: number;
  name?: string;
  x?: number;
  y?: number;
};


@Service()
export class H2dLoadingMaskService {
  private cocraft: CoCraftBridgeAPI;

  constructor(
    private editorService: EditorService,
    private widgetService: WidgetService,
  ) {
    this.cocraft = this.editorService.getCocraft();
  }

  public create(options: H2dLoadingMaskConfig | H2dLoadingMaskConfig[]) {
    const optArray = Array.isArray(options) ? options : [options];
    const { x: blankX, y: blankY } = calcBlankPosition(this.cocraft);

    // 累计宽度
    let cumulativeWidth = 0;
    const allFrames: FrameNode[] = [];

    optArray.forEach((opt = {}) => {
      const frame = this.cocraft.createFrame();

      const maskId = `h2d-loading-mask-${opt.name}-${Math.random().toString(36)}`;

      const mergeX = opt.x ?? blankX + cumulativeWidth;
      const mergeY = opt.y ?? blankY;
      const type = opt.type ?? 'app';
      const size = {
        width: opt.width ?? DEFAULT_LOADING_MASK_SIZE[type].width,
        height: opt.height ?? DEFAULT_LOADING_MASK_SIZE[type].height,
      };
      cumulativeWidth += size.width + OUTER_GAP;
      frame.resize(size.width, size.height);
      assignObject(frame, {
        name: opt.name,
        x: rounding(mergeX),
        y: rounding(mergeY),
        fills: DEFAULT_LOADING_MASK_OPTIONS.fills,
      });

      allFrames.push(frame);
      this.cocraft.currentPage.appendChild(frame);

      this.widgetService.float({
        name: maskId,
        component: <LoadingMask width={size.width} height={size.height} position={{ x: mergeX, y: mergeY }} />,
      });
    });

    this.cocraft.viewport.scrollAndZoomIntoView(allFrames);
    return allFrames;
  }
}


function LoadingMask(props: { width: number; height: number; position: { x: number; y: number } }) {
  const { width, height, position } = props;
  const zoom = useCurrentZoom();
  const bounds = useCurrentBounds();
  const marginLeft = useObservableState<number>(BuiltinUiState.EditorZoneMarginLeft, defaultMarginLeft);

  return (
    <div className="h-full left-0 right-0 overflow-hidden absolute pointer-events-none mx-1 my-2 h2d-loading-mask">
      {!!bounds.x && !!bounds.y && (
        <div
          className="relative will-change-transform bg-gradient-to-t from-[#D3F1D2] to-[#E5F2CD] pointer-events-auto overflow-hidden"
          style={{
            width: width * zoom, height: height * zoom,
            transform: `translate(${(-bounds.x * zoom - (marginLeft - defaultMarginLeft) + position.x * zoom)}px, ${
              -bounds.y * zoom + position.y * zoom
            }px)`,
          }}
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          {/* 斜向动画层 */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/20 via-white/5 to-transparent animate-pulse"></div>
          <div
            className="absolute w-full h-full animate-shimmer"
            style={{
              background: 'linear-gradient(135deg, transparent 30%, rgba(255,255,255,0.4) 50%, transparent 70%)',
            }}
          ></div>
        </div>
      )}
    </div>
  );
}
