import type { Config } from 'tailwindcss';
import * as animate from 'tailwindcss-animate';
import colors from './tailwind.color';
import fontSize from './tailwind.font';

// We want each package to be responsible for its own content.
const config: Omit<Config, 'content'> = {
  darkMode: ['class'],
  theme: {
    extend: {
      colors,
      borderRadius: {
        xs: 'var(--radius-xs)',
        sm: 'var(--radius-sm)',
        default: 'var(--radius-default)',
        md: 'var(--radius-md)',
        lg: 'var(--radius-lg)',
        xl: 'var(--radius-xl)',
      },
      fontSize,
      fontFamily: {
        body: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'system-ui',
          '"pingfang SC"',
          '"hiragino sans gb"',
          '"microsoft yahei ui"',
          '"microsoft yahei"',
          '"helvetica neue"',
          'arial',
          'ui-sans-serif',
        ],
        // 添加 emoji 字体栈
        emoji: [
          'Apple Color Emoji', // macOS & iOS
          'Segoe UI Emoji', // Windows
          'Noto Color Emoji', // Linux (Android备用)
          'Android Emoji', // Android
          'Twemoji Mozilla', // 跨平台Twemoji
          'EmojiOne Color', // 备选方案
          'sans-serif', // 最终回退
        ],
      },
      boxShadow: {
        default: 'var(--cd-shadow-default)',
        'drag-hover': 'var(--cd-shadow-drag-hover)',
        'drop-down': 'var(--cd-shadow-drop-down)',
        'dialog-tips': 'var(--cd-shadow-dialog-tips)',
        'item-layers-section': 'var(--cd-shadow-item-layers-section)',
        'item-default': 'var(--cd-shadow-item-default)',
        'item-comment-pin': 'var(--cd-shadow-item-comment-pin)',
        'item-create-comment': 'var(--cd-shadow-item-create-comment)',
        'item-comment-reply': 'var(--cd-shadow-item-comment-reply)',
        'popover-header': 'var(--cd-shadow-popover-header)',
        'cursor-name-container': 'var(--cd-shadow-cursor-name-container)',
        // 以下 slider 专用
        'slider-rail-before': 'var(--color-border-strong) 0 0 0 0.6px inset',
        'slider-rail-before-delta': 'var(--color-icon-tertiary) 0 0 0 0.6px inset',
        'slider-button': 'rgb(0 0 0 / 20%) 0 0 0 0.6px',
        'slider-button-active': 'rgb(0 0 0 / 20%) 0 0 0 0.6px',
        'slider-button-reset-active': 'rgb(0 0 0 / 20%) 0 0 0 0.6px',
      },
      zIndex: {
        root: 'var(--z-root)',
        widget: 'var(--z-widget)',
        toolbar: 'var(--z-toolbar)',
        dialog: 'var(--z-dialog)',
        toast: 'var(--z-toast)',
      },
      keyframes: {
        scan: {
          '0%': { top: '0' },
          '100%': { top: '100%' },
        },
        shimmer: {
          '0%': {
            transform: 'translateX(-100%) translateY(-100%)',
            opacity: '0',
          },
          '50%': {
            opacity: '1',
          },
          '100%': {
            transform: 'translateX(100%) translateY(100%)',
            opacity: '0',
          },
        },
      },
      animation: {
        scan: 'scan 2.5s linear infinite',
        shimmer: 'shimmer 2s ease-in-out infinite',
      },
    },
  },
  plugins: [animate],
};
export default config;
